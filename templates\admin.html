<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员面板 - 梦羽绘图工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .admin-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 20px auto;
            max-width: 1400px;
        }

        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px 20px 0 0;
            padding: 30px;
        }

        .nav-tabs .nav-link {
            border: none;
            color: #666;
            font-weight: 500;
            padding: 15px 25px;
            margin-right: 10px;
            border-radius: 10px 10px 0 0;
            transition: all 0.3s ease;
        }

        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }

        .nav-tabs .nav-link:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .tab-content {
            padding: 30px;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            margin-bottom: 20px;
        }

        .card-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px 15px 0 0;
            border: none;
            padding: 20px;
            font-weight: 600;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
        }

        .table thead th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: none;
            font-weight: 600;
            color: #495057;
        }

        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .alert {
            border-radius: 10px;
            border: none;
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .stats-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .redemption-stats-card {
            transition: transform 0.2s ease-in-out;
        }

        .redemption-stats-card:hover {
            transform: translateY(-3px);
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50px;
            padding: 12px 20px;
            color: #667eea;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 1);
            color: #764ba2;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <a href="/" class="back-btn">
        <i class="fas fa-arrow-left me-2"></i>返回主页
    </a>

    <div class="container-fluid">
        <div class="admin-container">
            <div class="admin-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h2 class="mb-0">
                            <i class="fas fa-cogs me-3"></i>管理员面板
                        </h2>
                        <p class="mb-0 mt-2 opacity-75">系统管理与配置中心</p>
                    </div>
                    <div class="col-auto">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-user-shield me-2"></i>
                            <span>{{ current_user.username }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="admin-body">
                <ul class="nav nav-tabs" id="adminTabs">
                    <li class="nav-item">
                        <a class="nav-link active" data-bs-toggle="tab" href="#usersTab">
                            <i class="fas fa-users me-2"></i>用户管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#userApprovalTab">
                            <i class="fas fa-user-check me-2"></i>用户审核
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#statisticsTab">
                            <i class="fas fa-chart-bar me-2"></i>系统统计
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#redemptionTab">
                            <i class="fas fa-ticket-alt me-2"></i>兑换码管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#settingsTab">
                            <i class="fas fa-cog me-2"></i>系统设置
                        </a>
                    </li>
                </ul>

                <div class="tab-content" id="adminTabContent">
                    <!-- 用户管理标签页 -->
                    <div class="tab-pane fade show active" id="usersTab">
                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-users me-2"></i>用户列表
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="usersTable">
                                            <div class="text-center">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">加载中...</span>
                                                </div>
                                                <p class="mt-2">正在加载用户数据...</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 积分充值 -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-coins me-2"></i>积分充值
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="chargeUsername" class="form-label">用户名</label>
                                            <input type="text" class="form-control" id="chargeUsername" placeholder="输入用户名">
                                        </div>
                                        <div class="mb-3">
                                            <label for="chargePoints" class="form-label">积分数量</label>
                                            <input type="number" class="form-control" id="chargePoints" placeholder="输入积分数量" min="1">
                                        </div>
                                        <div class="mb-3">
                                            <label for="chargeReason" class="form-label">充值原因</label>
                                            <input type="text" class="form-control" id="chargeReason" placeholder="充值原因（可选）">
                                        </div>
                                        <button id="chargeBtn" class="btn btn-primary">
                                            <i class="fas fa-plus me-1"></i>充值积分
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 用户审核标签页 -->
                    <div class="tab-pane fade" id="userApprovalTab">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-user-check me-2"></i>待审核用户
                                </h5>
                                <button id="refreshPendingUsersBtn" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-sync-alt me-1"></i>刷新
                                </button>
                            </div>
                            <div class="card-body">
                                <div id="pendingUsersTable">
                                    <div class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                        <p class="mt-2">正在加载待审核用户...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 系统统计标签页 -->
                    <div class="tab-pane fade" id="statisticsTab">
                        <div id="statisticsContent">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2">正在加载统计数据...</p>
                            </div>
                        </div>
                    </div>

                    <!-- 兑换码管理标签页 -->
                    <div class="tab-pane fade" id="redemptionTab">
                        <!-- 兑换码管理头部 -->
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h5 class="mb-0">
                                <i class="fas fa-ticket-alt me-2"></i>兑换码管理
                            </h5>
                            <div class="btn-group">
                                <button id="createCodeBtn" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>创建兑换码
                                </button>
                                <button id="batchCreateBtn" class="btn btn-outline-primary">
                                    <i class="fas fa-layer-group me-1"></i>批量创建
                                </button>
                            </div>
                        </div>

                        <!-- 兑换码统计 -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card redemption-stats-card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-ticket-alt fa-2x mb-2"></i>
                                        <h3 id="totalCodesCount" class="mb-1">-</h3>
                                        <small>总兑换码数</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card redemption-stats-card bg-success text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                                        <h3 id="activeCodesCount" class="mb-1">-</h3>
                                        <small>有效兑换码</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card redemption-stats-card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-clock fa-2x mb-2"></i>
                                        <h3 id="usedCodesCount" class="mb-1">-</h3>
                                        <small>已使用</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card redemption-stats-card bg-info text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-coins fa-2x mb-2"></i>
                                        <h3 id="totalPointsDistributed" class="mb-1">-</h3>
                                        <small>总发放积分</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 兑换码列表 -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">兑换码列表</h6>
                            </div>
                            <div class="card-body">
                                <div id="redemptionCodesTable">
                                    <div class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                        <p class="mt-2">正在加载兑换码数据...</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 最近使用记录 -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h6 class="mb-0">最近使用记录</h6>
                            </div>
                            <div class="card-body">
                                <div id="recentRedemptions">
                                    <div class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                        <p class="mt-2">正在加载使用记录...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 系统设置标签页 -->
                    <div class="tab-pane fade" id="settingsTab">
                        <div class="row">
                            <!-- 代理设置 -->
                            <div class="col-12 mb-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-network-wired me-2"></i>代理池设置
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="globalProxyApiUrl" class="form-label">全局代理池API地址</label>
                                            <input type="url" class="form-control" id="globalProxyApiUrl"
                                                   placeholder="输入代理池API地址，例如：http://example.com/api/proxy">
                                            <div class="form-text">
                                                配置后，所有用户的请求都将通过此代理池进行处理。留空则使用直连。
                                            </div>
                                        </div>
                                        <button id="saveProxySettingsBtn" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>保存代理设置
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 积分设置 -->
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-coins me-2"></i>积分消耗设置
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="imageGenerationCost" class="form-label">图像生成消耗积分</label>
                                            <input type="number" class="form-control" id="imageGenerationCost" min="1" value="1">
                                        </div>
                                        <div class="mb-3">
                                            <label for="videoGenerationCost" class="form-label">视频生成消耗积分</label>
                                            <input type="number" class="form-control" id="videoGenerationCost" min="1" value="5">
                                        </div>
                                        <button id="savePointsSettingsBtn" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>保存积分设置
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 注册设置 -->
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-user-plus me-2"></i>用户注册设置
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="requireRegistrationApproval">
                                            <label class="form-check-label" for="requireRegistrationApproval">
                                                新用户注册需要管理员审核
                                            </label>
                                            <div class="form-text">开启后，新注册的用户需要等待管理员审核通过才能使用系统</div>
                                        </div>
                                        <button id="saveRegistrationSettingsBtn" class="btn btn-primary mt-3">
                                            <i class="fas fa-save me-1"></i>保存注册设置
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建兑换码模态框 -->
    <div class="modal fade" id="createCodeModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建兑换码</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="codePoints" class="form-label">积分数量</label>
                        <input type="number" class="form-control" id="codePoints" min="1" value="10">
                    </div>
                    <div class="mb-3">
                        <label for="codeExpireDays" class="form-label">有效期（天）</label>
                        <input type="number" class="form-control" id="codeExpireDays" min="1" value="30">
                    </div>
                    <div class="mb-3">
                        <label for="codeDescription" class="form-label">描述（可选）</label>
                        <input type="text" class="form-control" id="codeDescription" placeholder="兑换码描述">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmCreateCodeBtn">创建</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量创建兑换码模态框 -->
    <div class="modal fade" id="batchCreateModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">批量创建兑换码</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="batchCodeCount" class="form-label">创建数量</label>
                                <input type="number" class="form-control" id="batchCodeCount" min="1" max="100" value="10">
                                <div class="form-text">最多一次创建100个</div>
                            </div>
                            <div class="mb-3">
                                <label for="batchCodePoints" class="form-label">每个积分数量</label>
                                <input type="number" class="form-control" id="batchCodePoints" min="1" value="10">
                            </div>
                            <div class="mb-3">
                                <label for="batchCodeExpireDays" class="form-label">有效期（天）</label>
                                <input type="number" class="form-control" id="batchCodeExpireDays" min="1" value="30">
                            </div>
                            <div class="mb-3">
                                <label for="batchCodeDescription" class="form-label">描述（可选）</label>
                                <input type="text" class="form-control" id="batchCodeDescription" placeholder="批量兑换码描述">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="mb-0">预览</h6>
                                </div>
                                <div class="card-body">
                                    <p><strong>数量：</strong><span id="previewCount">10</span> 个</p>
                                    <p><strong>每个积分：</strong><span id="previewPoints">10</span> 点</p>
                                    <p><strong>过期时间：</strong><span id="previewExpireDate">-</span></p>
                                    <p><strong>总积分：</strong><span id="previewTotalPoints">100</span> 点</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmBatchCreateBtn">批量创建</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 页面加载完成后自动加载管理员数据
            loadAdminData();

            // 初始化兑换码管理功能
            initRedemptionCodeManagement();

            // ==================== 兑换码管理相关函数 ====================

            // 显示兑换码统计
            window.displayRedemptionStats = function(stats) {
                // 更新统计卡片
                document.getElementById('totalCodesCount').textContent = stats.total_codes || 0;
                document.getElementById('activeCodesCount').textContent = stats.active_codes || 0;
                document.getElementById('usedCodesCount').textContent = stats.total_codes_used || 0;
                document.getElementById('totalPointsDistributed').textContent = stats.total_points_distributed || 0;
            };

            // 显示最近使用记录
            window.displayRecentRedemptions = function(records) {
                const container = document.getElementById('recentRedemptions');

                if (records.length === 0) {
                    container.innerHTML = '<div class="text-center p-4"><p class="text-muted mb-0">暂无使用记录</p></div>';
                    return;
                }

                let html = '<div class="table-responsive"><table class="table table-hover"><thead><tr><th>用户</th><th>兑换码</th><th>积分</th><th>类型</th><th>使用时间</th></tr></thead><tbody>';
                records.slice(0, 20).forEach((record) => {
                    const date = new Date(record.used_at).toLocaleString();
                    const typeText = record.code_type === 'one_time' ? '一次性' : '活动码';
                    const typeClass = record.code_type === 'one_time' ? 'bg-primary' : 'bg-success';

                    html += `
                        <tr>
                            <td><strong>${record.username}</strong></td>
                            <td><code>${record.code}</code></td>
                            <td><span class="text-success fw-bold">+${record.points}</span></td>
                            <td><span class="badge ${typeClass}">${typeText}</span></td>
                            <td><small class="text-muted">${date}</small></td>
                        </tr>
                    `;
                });
                html += '</tbody></table></div>';

                container.innerHTML = html;
            };

            // 显示兑换码列表
            window.displayRedemptionCodesTable = function(codes) {
                const container = document.getElementById('redemptionCodesTable');

                if (codes.length === 0) {
                    container.innerHTML = '<div class="text-center p-4"><p class="text-muted mb-0">暂无兑换码</p></div>';
                    return;
                }

                let html = `
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <button class="btn btn-outline-primary btn-sm" onclick="selectAllCodes()">
                                <i class="fas fa-check-square me-1"></i>全选
                            </button>
                            <button class="btn btn-outline-secondary btn-sm ms-2" onclick="clearSelection()">
                                <i class="fas fa-square me-1"></i>清空
                            </button>
                        </div>
                        <div>
                            <button class="btn btn-outline-success btn-sm" onclick="batchActivateCodes()">
                                <i class="fas fa-play me-1"></i>批量启用
                            </button>
                            <button class="btn btn-outline-warning btn-sm ms-1" onclick="batchDeactivateCodes()">
                                <i class="fas fa-pause me-1"></i>批量禁用
                            </button>
                            <button class="btn btn-outline-danger btn-sm ms-1" onclick="batchDeleteCodes()">
                                <i class="fas fa-trash me-1"></i>批量删除
                            </button>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th width="50"><input type="checkbox" id="selectAllCheckbox" onchange="toggleAllCodes(this)"></th>
                                    <th>兑换码</th>
                                    <th>积分</th>
                                    <th>类型</th>
                                    <th>状态</th>
                                    <th>使用次数</th>
                                    <th>过期时间</th>
                                    <th>描述</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                codes.forEach((code) => {
                    const expireDate = new Date(code.expire_at);
                    const isExpired = new Date() > expireDate;
                    const createDate = new Date(code.created_at);

                    // 状态判断
                    let statusText, statusClass, statusIcon;
                    if (isExpired) {
                        statusText = '已过期';
                        statusClass = 'bg-danger';
                        statusIcon = 'fas fa-times-circle';
                    } else if (!code.is_active) {
                        statusText = '已禁用';
                        statusClass = 'bg-warning';
                        statusIcon = 'fas fa-pause-circle';
                    } else if (code.type === 'one_time' && code.used_count > 0) {
                        statusText = '已使用';
                        statusClass = 'bg-secondary';
                        statusIcon = 'fas fa-check-circle';
                    } else {
                        statusText = '有效';
                        statusClass = 'bg-success';
                        statusIcon = 'fas fa-check-circle';
                    }

                    const typeText = code.type === 'one_time' ? '一次性' : '活动码';
                    const typeClass = code.type === 'one_time' ? 'bg-primary' : 'bg-info';

                    html += `
                        <tr>
                            <td><input type="checkbox" class="code-checkbox" value="${code.code}"></td>
                            <td><code>${code.code}</code><br><small class="text-muted">${createDate.toLocaleDateString()}</small></td>
                            <td><strong>${code.points}</strong></td>
                            <td><span class="badge ${typeClass}">${typeText}</span></td>
                            <td><span class="badge ${statusClass}"><i class="${statusIcon} me-1"></i>${statusText}</span></td>
                            <td>${code.used_count}</td>
                            <td class="${isExpired ? 'text-danger' : ''}">${expireDate.toLocaleDateString()}</td>
                            <td><small title="${code.description || '无描述'}">${code.description ? (code.description.length > 15 ? code.description.substring(0, 15) + '...' : code.description) : '无描述'}</small></td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    ${!isExpired ? `
                                        <button class="btn btn-outline-${code.is_active ? 'warning' : 'success'}" onclick="toggleRedemptionCode('${code.code}', '${code.is_active ? 'deactivate' : 'activate'}')" title="${code.is_active ? '禁用' : '启用'}">
                                            <i class="fas fa-${code.is_active ? 'pause' : 'play'}"></i>
                                        </button>
                                    ` : ''}
                                    <button class="btn btn-outline-primary" onclick="copyCodeToClipboard('${code.code}')" title="复制">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="deleteRedemptionCode('${code.code}')" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `;
                });

                html += '</tbody></table></div>';
                container.innerHTML = html;
            };

            // 加载兑换码数据
            window.loadRedemptionData = function() {
                // 加载兑换码统计
                fetch('/admin/redemption_statistics')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displayRedemptionStats(data.statistics);
                        } else {
                            console.error('加载统计数据失败:', data.message);
                            // 显示错误状态
                            document.getElementById('totalCodesCount').textContent = '-';
                            document.getElementById('activeCodesCount').textContent = '-';
                            document.getElementById('usedCodesCount').textContent = '-';
                            document.getElementById('totalPointsDistributed').textContent = '-';
                        }
                    })
                    .catch(error => {
                        console.error('加载统计数据失败:', error);
                        // 显示错误状态
                        document.getElementById('totalCodesCount').textContent = '-';
                        document.getElementById('activeCodesCount').textContent = '-';
                        document.getElementById('usedCodesCount').textContent = '-';
                        document.getElementById('totalPointsDistributed').textContent = '-';
                    });

                // 加载兑换码列表
                fetch('/admin/redemption_codes')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displayRedemptionCodesTable(data.codes);
                        } else {
                            const container = document.getElementById('redemptionCodesTable');
                            if (container) {
                                container.innerHTML = `<div class="alert alert-danger m-3">${data.message}</div>`;
                            }
                        }
                    })
                    .catch(error => {
                        const container = document.getElementById('redemptionCodesTable');
                        if (container) {
                            container.innerHTML = `<div class="alert alert-danger m-3">加载兑换码失败: ${error}</div>`;
                        }
                    });

                // 加载使用记录
                fetch('/admin/redemption_usage_records')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displayRecentRedemptions(data.records);
                        } else {
                            const container = document.getElementById('recentRedemptions');
                            if (container) {
                                container.innerHTML = `<div class="alert alert-danger m-3">${data.message}</div>`;
                            }
                        }
                    })
                    .catch(error => {
                        const container = document.getElementById('recentRedemptions');
                        if (container) {
                            container.innerHTML = `<div class="alert alert-danger m-3">加载使用记录失败: ${error}</div>`;
                        }
                    });
            };

            // 加载管理员数据
            function loadAdminData() {
                // 加载用户列表
                fetch('/admin/users')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displayUsersTable(data.users);
                        } else {
                            document.getElementById('usersTable').innerHTML = `<div class="alert alert-danger">加载用户数据失败: ${data.message}</div>`;
                        }
                    })
                    .catch(error => {
                        document.getElementById('usersTable').innerHTML = `<div class="alert alert-danger">加载用户数据失败: ${error}</div>`;
                    });

                // 加载统计信息
                fetch('/admin/statistics')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displayStatistics(data.statistics);
                        } else {
                            document.getElementById('statisticsContent').innerHTML = `<div class="alert alert-danger">加载统计数据失败: ${data.message}</div>`;
                        }
                    })
                    .catch(error => {
                        document.getElementById('statisticsContent').innerHTML = `<div class="alert alert-danger">加载统计数据失败: ${error}</div>`;
                    });

                // 加载待审核用户
                fetch('/admin/pending_users')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displayPendingUsersTable(data.pending_users);
                        } else {
                            document.getElementById('pendingUsersTable').innerHTML = `<div class="alert alert-danger">加载待审核用户失败: ${data.message}</div>`;
                        }
                    })
                    .catch(error => {
                        document.getElementById('pendingUsersTable').innerHTML = `<div class="alert alert-danger">加载待审核用户失败: ${error}</div>`;
                    });

                // 加载系统设置
                fetch('/admin/settings')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            document.getElementById('globalProxyApiUrl').value = data.settings.global_proxy_api_url || '';
                            document.getElementById('imageGenerationCost').value = data.settings.image_generation_cost || 1;
                            document.getElementById('videoGenerationCost').value = data.settings.video_generation_cost || 5;
                            document.getElementById('requireRegistrationApproval').checked = data.settings.require_registration_approval || false;
                        }
                    })
                    .catch(error => {
                        console.error('加载系统设置失败:', error);
                    });

                // 加载兑换码数据
                loadRedemptionData();
            }

            // 显示用户表格
            function displayUsersTable(users) {
                let html = `
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>用户名</th>
                                    <th>邮箱</th>
                                    <th>积分</th>
                                    <th>生成次数</th>
                                    <th>注册时间</th>
                                    <th>最后登录</th>
                                    <th>管理员</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                users.forEach(user => {
                    html += `
                        <tr>
                            <td><strong>${user.username}</strong></td>
                            <td>${user.email || '-'}</td>
                            <td>${user.points}</td>
                            <td>${user.total_generated}</td>
                            <td>${user.created_at ? new Date(user.created_at).toLocaleDateString() : '-'}</td>
                            <td>${user.last_login ? new Date(user.last_login).toLocaleDateString() : '-'}</td>
                            <td>${user.is_admin ? '是' : '否'}</td>
                        </tr>
                    `;
                });

                html += `
                            </tbody>
                        </table>
                    </div>
                `;

                document.getElementById('usersTable').innerHTML = html;
            }

            // 显示统计信息
            function displayStatistics(stats) {
                const html = `
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number">${stats.total_users}</div>
                                <div class="stats-label">总用户数</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number">${stats.total_generations}</div>
                                <div class="stats-label">总生成次数</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number">${stats.total_points_consumed}</div>
                                <div class="stats-label">总消耗积分</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number">${stats.active_users_today}</div>
                                <div class="stats-label">今日活跃用户</div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">最近活动</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>用户</th>
                                                    <th>操作</th>
                                                    <th>时间</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${stats.recent_activities.map(activity => `
                                                    <tr>
                                                        <td>${activity.username}</td>
                                                        <td>${activity.action}</td>
                                                        <td>${new Date(activity.timestamp).toLocaleString()}</td>
                                                    </tr>
                                                `).join('')}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                document.getElementById('statisticsContent').innerHTML = html;
            }

            // 显示待审核用户表格
            function displayPendingUsersTable(pendingUsers) {
                if (pendingUsers.length === 0) {
                    document.getElementById('pendingUsersTable').innerHTML = '<div class="text-center p-4"><p class="text-muted mb-0">暂无待审核用户</p></div>';
                    return;
                }

                let html = `
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>用户名</th>
                                    <th>邮箱</th>
                                    <th>注册时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                pendingUsers.forEach(user => {
                    html += `
                        <tr>
                            <td><strong>${user.username}</strong></td>
                            <td>${user.email || '-'}</td>
                            <td>${new Date(user.created_at).toLocaleString()}</td>
                            <td>
                                <button class="btn btn-success btn-sm me-2" onclick="approveUser('${user.username}')">
                                    <i class="fas fa-check me-1"></i>通过
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="rejectUser('${user.username}')">
                                    <i class="fas fa-times me-1"></i>拒绝
                                </button>
                            </td>
                        </tr>
                    `;
                });

                html += `
                            </tbody>
                        </table>
                    </div>
                `;

                document.getElementById('pendingUsersTable').innerHTML = html;
            }

            // 积分充值按钮事件
            const chargeBtn = document.getElementById('chargeBtn');
            if (chargeBtn) {
                chargeBtn.addEventListener('click', function() {
                    const username = document.getElementById('chargeUsername').value.trim();
                    const points = parseInt(document.getElementById('chargePoints').value);
                    const reason = document.getElementById('chargeReason').value.trim() || '管理员充值';

                    if (!username || !points || points <= 0) {
                        alert('请填写正确的用户名和积分数');
                        return;
                    }

                    fetch('/admin/add_points', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            username: username,
                            points: points,
                            reason: reason
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message);
                        if (data.success) {
                            // 清空表单
                            document.getElementById('chargeUsername').value = '';
                            document.getElementById('chargePoints').value = '';
                            document.getElementById('chargeReason').value = '';
                            // 重新加载数据
                            loadAdminData();
                        }
                    })
                    .catch(error => {
                        alert('操作失败: ' + error);
                    });
                });
            }

            // 保存代理设置
            const saveProxySettingsBtn = document.getElementById('saveProxySettingsBtn');
            if (saveProxySettingsBtn) {
                saveProxySettingsBtn.addEventListener('click', function() {
                    const globalProxyApiUrl = document.getElementById('globalProxyApiUrl').value.trim();

                    fetch('/admin/settings', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            global_proxy_api_url: globalProxyApiUrl
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message);
                    })
                    .catch(error => {
                        alert('保存失败: ' + error);
                    });
                });
            }

            // 保存积分设置
            const savePointsSettingsBtn = document.getElementById('savePointsSettingsBtn');
            if (savePointsSettingsBtn) {
                savePointsSettingsBtn.addEventListener('click', function() {
                    const imageGenerationCost = parseInt(document.getElementById('imageGenerationCost').value);
                    const videoGenerationCost = parseInt(document.getElementById('videoGenerationCost').value);

                    if (imageGenerationCost < 1 || videoGenerationCost < 1) {
                        alert('积分消耗必须大于0');
                        return;
                    }

                    fetch('/admin/settings', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            image_generation_cost: imageGenerationCost,
                            video_generation_cost: videoGenerationCost
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message);
                    })
                    .catch(error => {
                        alert('保存失败: ' + error);
                    });
                });
            }

            // 保存注册设置
            const saveRegistrationSettingsBtn = document.getElementById('saveRegistrationSettingsBtn');
            if (saveRegistrationSettingsBtn) {
                saveRegistrationSettingsBtn.addEventListener('click', function() {
                    const requireRegistrationApproval = document.getElementById('requireRegistrationApproval').checked;

                    fetch('/admin/settings', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            require_registration_approval: requireRegistrationApproval
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message);
                    })
                    .catch(error => {
                        alert('保存失败: ' + error);
                    });
                });
            }

            // 刷新待审核用户按钮
            const refreshPendingUsersBtn = document.getElementById('refreshPendingUsersBtn');
            if (refreshPendingUsersBtn) {
                refreshPendingUsersBtn.addEventListener('click', function() {
                    fetch('/admin/pending_users')
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                displayPendingUsersTable(data.pending_users);
                            } else {
                                alert('刷新失败: ' + data.message);
                            }
                        })
                        .catch(error => {
                            alert('刷新失败: ' + error);
                        });
                });
            }

            // 监听兑换码管理标签页激活事件
            const redemptionTabTrigger = document.querySelector('a[href="#redemptionTab"]');
            if (redemptionTabTrigger) {
                redemptionTabTrigger.addEventListener('shown.bs.tab', function() {
                    console.log('兑换码管理标签页已激活，重新初始化');
                    setTimeout(initRedemptionCodeManagement, 100);
                });
            }

            // 如果兑换码标签页已经是激活状态，直接初始化
            if (document.querySelector('#redemptionTab.active')) {
                setTimeout(initRedemptionCodeManagement, 100);
            }
        });

        // 审核通过用户
        function approveUser(username) {
            if (confirm(`确定要通过用户 "${username}" 的审核吗？`)) {
                fetch('/admin/approve_user', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username
                    })
                })
                .then(response => response.json())
                .then(data => {
                    alert(data.message);
                    if (data.success) {
                        // 重新加载待审核用户列表
                        fetch('/admin/pending_users')
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    displayPendingUsersTable(data.pending_users);
                                }
                            });
                    }
                })
                .catch(error => {
                    alert('操作失败: ' + error);
                });
            }
        }

        // 拒绝用户
        function rejectUser(username) {
            const reason = prompt(`请输入拒绝用户 "${username}" 的原因（可选）:`);
            if (reason !== null) { // 用户没有取消
                fetch('/admin/reject_user', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        reason: reason
                    })
                })
                .then(response => response.json())
                .then(data => {
                    alert(data.message);
                    if (data.success) {
                        // 重新加载待审核用户列表
                        fetch('/admin/pending_users')
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    displayPendingUsersTable(data.pending_users);
                                }
                            });
                    }
                })
                .catch(error => {
                    alert('操作失败: ' + error);
                });
            }
        }

        // 兑换码管理相关函数
        function initRedemptionCodeManagement() {
            // 创建兑换码按钮
            const createCodeBtn = document.getElementById('createCodeBtn');
            const createCodeModal = new bootstrap.Modal(document.getElementById('createCodeModal'));

            if (createCodeBtn) {
                createCodeBtn.addEventListener('click', function() {
                    createCodeModal.show();
                });
            }

            // 批量创建按钮
            const batchCreateBtn = document.getElementById('batchCreateBtn');
            const batchCreateModal = new bootstrap.Modal(document.getElementById('batchCreateModal'));

            if (batchCreateBtn) {
                batchCreateBtn.addEventListener('click', function() {
                    updateBatchPreview();
                    batchCreateModal.show();
                });
            }

            // 批量创建预览更新
            const batchInputs = ['batchCodeCount', 'batchCodePoints', 'batchCodeExpireDays'];
            batchInputs.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('input', updateBatchPreview);
                }
            });

            // 确认创建单个兑换码
            const confirmCreateCodeBtn = document.getElementById('confirmCreateCodeBtn');
            if (confirmCreateCodeBtn) {
                confirmCreateCodeBtn.addEventListener('click', function() {
                    const points = parseInt(document.getElementById('codePoints').value);
                    const expireDays = parseInt(document.getElementById('codeExpireDays').value);
                    const description = document.getElementById('codeDescription').value.trim();

                    if (!points || points <= 0 || !expireDays || expireDays <= 0) {
                        alert('请填写正确的积分数量和有效期');
                        return;
                    }

                    this.disabled = true;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>生成中...';

                    fetch('/admin/create_redemption_codes', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            count: 1,
                            points: points,
                            expire_days: expireDays,
                            description: description,
                            type: 'one_time'
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert(`成功创建兑换码: ${data.codes[0]}`);
                            createCodeModal.hide();
                            // 清空表单
                            document.getElementById('codePoints').value = '10';
                            document.getElementById('codeExpireDays').value = '30';
                            document.getElementById('codeDescription').value = '';
                            // 重新加载数据
                            loadRedemptionData();
                        } else {
                            alert('创建失败: ' + data.message);
                        }
                    })
                    .catch(error => {
                        alert('创建失败: ' + error);
                    })
                    .finally(() => {
                        this.disabled = false;
                        this.innerHTML = '创建';
                    });
                });
            }

            // 确认批量创建兑换码
            const confirmBatchCreateBtn = document.getElementById('confirmBatchCreateBtn');
            if (confirmBatchCreateBtn) {
                confirmBatchCreateBtn.addEventListener('click', function() {
                    const count = parseInt(document.getElementById('batchCodeCount').value);
                    const points = parseInt(document.getElementById('batchCodePoints').value);
                    const expireDays = parseInt(document.getElementById('batchCodeExpireDays').value);
                    const description = document.getElementById('batchCodeDescription').value.trim();

                    if (!count || count <= 0 || count > 100 || !points || points <= 0 || !expireDays || expireDays <= 0) {
                        alert('请填写正确的参数（数量1-100，积分>0，有效期>0）');
                        return;
                    }

                    this.disabled = true;
                    this.textContent = '生成中...';

                    fetch('/admin/create_redemption_codes', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            count: count,
                            points: points,
                            expire_days: expireDays,
                            description: description,
                            type: 'one_time'
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert(`成功创建 ${data.codes.length} 个兑换码`);
                            batchCreateModal.hide();
                            // 清空表单
                            document.getElementById('batchCodeCount').value = '10';
                            document.getElementById('batchCodePoints').value = '10';
                            document.getElementById('batchCodeExpireDays').value = '30';
                            document.getElementById('batchCodeDescription').value = '';
                            // 重新加载数据
                            loadRedemptionData();
                        } else {
                            alert('创建失败: ' + data.message);
                        }
                    })
                    .catch(error => {
                        alert('创建失败: ' + error);
                    })
                    .finally(() => {
                        this.disabled = false;
                        this.textContent = '批量创建';
                    });
                });
            }
        }

        // 更新批量创建预览
        function updateBatchPreview() {
            const count = parseInt(document.getElementById('batchCodeCount').value) || 0;
            const points = parseInt(document.getElementById('batchCodePoints').value) || 0;
            const expireDays = parseInt(document.getElementById('batchCodeExpireDays').value) || 0;

            document.getElementById('previewCount').textContent = count;
            document.getElementById('previewPoints').textContent = points;
            document.getElementById('previewTotalPoints').textContent = points * count;

            if (expireDays > 0) {
                const expireDate = new Date();
                expireDate.setDate(expireDate.getDate() + expireDays);
                document.getElementById('previewExpireDate').textContent = expireDate.toLocaleDateString();
            } else {
                document.getElementById('previewExpireDate').textContent = '-';
            }
        }

        // 兑换码操作函数
        function toggleRedemptionCode(code, action) {
            if (!confirm(`确定要${action === 'activate' ? '启用' : '禁用'}这个兑换码吗？`)) {
                return;
            }

            fetch('/admin/toggle_redemption_code', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    code: code,
                    action: action
                })
            })
            .then(response => response.json())
            .then(data => {
                alert(data.message);
                if (data.success) {
                    loadRedemptionData();
                }
            })
            .catch(error => {
                alert('操作失败: ' + error);
            });
        }

        function deleteRedemptionCode(code) {
            if (!confirm(`确定要删除兑换码 ${code} 吗？此操作不可恢复！`)) {
                return;
            }

            fetch('/admin/delete_redemption_code', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    code: code
                })
            })
            .then(response => response.json())
            .then(data => {
                alert(data.message);
                if (data.success) {
                    loadRedemptionData();
                }
            })
            .catch(error => {
                alert('删除失败: ' + error);
            });
        }

        function copyCodeToClipboard(code) {
            navigator.clipboard.writeText(code).then(function() {
                alert('兑换码已复制到剪贴板');
            }, function() {
                // 备用方法
                const textArea = document.createElement('textarea');
                textArea.value = code;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('兑换码已复制到剪贴板');
            });
        }

        // 批量操作函数
        function selectAllCodes() {
            const checkboxes = document.querySelectorAll('.code-checkbox');
            checkboxes.forEach(cb => cb.checked = true);
            document.getElementById('selectAllCheckbox').checked = true;
        }

        function clearSelection() {
            const checkboxes = document.querySelectorAll('.code-checkbox');
            checkboxes.forEach(cb => cb.checked = false);
            document.getElementById('selectAllCheckbox').checked = false;
        }

        function toggleAllCodes(masterCheckbox) {
            const checkboxes = document.querySelectorAll('.code-checkbox');
            checkboxes.forEach(cb => cb.checked = masterCheckbox.checked);
        }

        function getSelectedCodes() {
            const checkboxes = document.querySelectorAll('.code-checkbox:checked');
            return Array.from(checkboxes).map(cb => cb.value);
        }

        function batchActivateCodes() {
            const codes = getSelectedCodes();
            if (codes.length === 0) {
                alert('请先选择要操作的兑换码');
                return;
            }
            if (confirm(`确定要启用选中的 ${codes.length} 个兑换码吗？`)) {
                batchOperationCodes(codes, 'activate');
            }
        }

        function batchDeactivateCodes() {
            const codes = getSelectedCodes();
            if (codes.length === 0) {
                alert('请先选择要操作的兑换码');
                return;
            }
            if (confirm(`确定要禁用选中的 ${codes.length} 个兑换码吗？`)) {
                batchOperationCodes(codes, 'deactivate');
            }
        }

        function batchDeleteCodes() {
            const codes = getSelectedCodes();
            if (codes.length === 0) {
                alert('请先选择要操作的兑换码');
                return;
            }
            if (confirm(`确定要删除选中的 ${codes.length} 个兑换码吗？此操作不可恢复！`)) {
                batchOperationCodes(codes, 'delete');
            }
        }

        function batchOperationCodes(codes, action) {
            const promises = codes.map(code => {
                let url, method, body;

                if (action === 'delete') {
                    url = '/admin/delete_redemption_code';
                    method = 'POST';
                    body = JSON.stringify({ code: code });
                } else {
                    url = '/admin/toggle_redemption_code';
                    method = 'POST';
                    body = JSON.stringify({ code: code, action: action });
                }

                return fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: body
                });
            });

            Promise.all(promises)
                .then(responses => Promise.all(responses.map(r => r.json())))
                .then(results => {
                    const successCount = results.filter(r => r.success).length;
                    const failCount = results.length - successCount;

                    if (failCount === 0) {
                        alert(`批量操作完成，成功处理 ${successCount} 个兑换码`);
                    } else {
                        alert(`批量操作完成，成功 ${successCount} 个，失败 ${failCount} 个`);
                    }

                    loadRedemptionData();
                    clearSelection();
                })
                .catch(error => {
                    alert('批量操作失败: ' + error);
                });
        }
    </script>
</body>
</html>
