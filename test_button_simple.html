<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试按钮</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <h2>测试创建兑换码按钮</h2>
        
        <div class="alert alert-info">
            <h5>测试步骤：</h5>
            <ol>
                <li>点击下面的"测试按钮功能"按钮</li>
                <li>观察控制台输出和模态框是否显示</li>
            </ol>
        </div>
        
        <button id="testBtn" class="btn btn-primary mb-3">
            <i class="fas fa-test me-1"></i>测试按钮功能
        </button>
        
        <button id="createCodeBtn" class="btn btn-success mb-3">
            <i class="fas fa-plus me-1"></i>创建兑换码
        </button>
        
        <div id="testResult" class="alert alert-secondary">
            <h6>测试结果：</h6>
            <div id="resultContent">等待测试...</div>
        </div>
    </div>

    <!-- 创建兑换码模态框 -->
    <div class="modal fade" id="createCodeModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus-circle me-2"></i>创建兑换码
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-success">
                        <h6>✅ 模态框显示成功！</h6>
                        <p>创建兑换码按钮工作正常。</p>
                    </div>
                    
                    <!-- 简化的表单 -->
                    <form>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="newCodeType" class="form-label">兑换码类型</label>
                                <select id="newCodeType" class="form-select">
                                    <option value="one_time">一次性兑换码</option>
                                    <option value="activity">活动兑换码</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="newCodePoints" class="form-label">积分数量</label>
                                <input type="number" id="newCodePoints" class="form-control" value="10">
                            </div>
                            <div class="col-md-6">
                                <label for="newCodeCount" class="form-label">生成数量</label>
                                <input type="number" id="newCodeCount" class="form-control" value="1">
                            </div>
                            <div class="col-md-6">
                                <label for="newCodeExpireDays" class="form-label">有效天数</label>
                                <input type="number" id="newCodeExpireDays" class="form-control" value="30">
                            </div>
                        </div>
                        
                        <!-- 预览区域 -->
                        <div class="mt-3 alert alert-info">
                            <h6>预览信息：</h6>
                            <p>类型: <span id="previewType">一次性</span></p>
                            <p>数量: <span id="previewCount">1</span></p>
                            <p>积分: <span id="previewPoints">10</span></p>
                            <p>过期日期: <span id="previewExpireDate">-</span></p>
                            <p>总积分: <span id="previewTotalPoints">10</span></p>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary">生成兑换码</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const resultContent = document.getElementById('resultContent');
            let testResults = [];
            
            // 更新创建兑换码预览
            function updateCreateCodePreview() {
                const type = document.getElementById('newCodeType').value;
                const points = parseInt(document.getElementById('newCodePoints').value) || 0;
                const count = parseInt(document.getElementById('newCodeCount').value) || 0;
                const days = parseInt(document.getElementById('newCodeExpireDays').value) || 0;
                
                const typeText = type === 'one_time' ? '一次性' : '活动';
                const expireDate = new Date();
                expireDate.setDate(expireDate.getDate() + days);
                
                document.getElementById('previewType').textContent = typeText;
                document.getElementById('previewCount').textContent = count;
                document.getElementById('previewPoints').textContent = points;
                document.getElementById('previewExpireDate').textContent = expireDate.toLocaleDateString();
                document.getElementById('previewTotalPoints').textContent = points * count;
            }
            
            // 初始化预览
            updateCreateCodePreview();
            
            // 绑定预览更新事件
            ['newCodeType', 'newCodePoints', 'newCodeCount', 'newCodeExpireDays'].forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('input', updateCreateCodePreview);
                    element.addEventListener('change', updateCreateCodePreview);
                }
            });
            
            // 测试按钮功能
            document.getElementById('testBtn').addEventListener('click', function() {
                testResults = [];
                testResults.push('🔍 开始测试...');
                
                // 检查创建兑换码按钮
                const createCodeBtn = document.getElementById('createCodeBtn');
                if (createCodeBtn) {
                    testResults.push('✅ 创建兑换码按钮找到');
                } else {
                    testResults.push('❌ 创建兑换码按钮未找到');
                    resultContent.innerHTML = testResults.join('<br>');
                    return;
                }
                
                // 检查模态框
                const modalElement = document.getElementById('createCodeModal');
                if (modalElement) {
                    testResults.push('✅ 模态框元素找到');
                } else {
                    testResults.push('❌ 模态框元素未找到');
                    resultContent.innerHTML = testResults.join('<br>');
                    return;
                }
                
                // 创建Bootstrap模态框实例
                let createCodeModal;
                try {
                    createCodeModal = new bootstrap.Modal(modalElement);
                    testResults.push('✅ Bootstrap模态框实例创建成功');
                } catch (error) {
                    testResults.push('❌ Bootstrap模态框实例创建失败: ' + error.message);
                    resultContent.innerHTML = testResults.join('<br>');
                    return;
                }
                
                // 绑定点击事件
                createCodeBtn.addEventListener('click', function() {
                    testResults.push('🔥 按钮被点击！');
                    resultContent.innerHTML = testResults.join('<br>');
                    
                    try {
                        createCodeModal.show();
                        testResults.push('✅ 模态框显示成功');
                        updateCreateCodePreview();
                        testResults.push('✅ 预览更新成功');
                    } catch (error) {
                        testResults.push('❌ 模态框显示失败: ' + error.message);
                    }
                    resultContent.innerHTML = testResults.join('<br>');
                });
                
                testResults.push('✅ 点击事件绑定成功');
                testResults.push('👆 现在可以点击"创建兑换码"按钮测试');
                resultContent.innerHTML = testResults.join('<br>');
            });
        });
    </script>
</body>
</html>
