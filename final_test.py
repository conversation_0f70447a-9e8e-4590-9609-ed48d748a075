#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试新的兑换码管理界面
"""

import requests
import json

def test_final():
    print("=== 最终测试新的兑换码管理界面 ===\n")
    
    base_url = 'http://localhost:7799'
    session = requests.Session()
    
    # 登录管理员
    print("1. 登录管理员账户...")
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    response = session.post(f'{base_url}/login', json=login_data)
    if response.status_code == 200 and response.json().get('success'):
        print("   ✅ 管理员登录成功")
    else:
        print(f"   ❌ 管理员登录失败")
        return
    
    # 测试统计API
    print("\n2. 测试兑换码统计API...")
    response = session.get(f'{base_url}/admin/redemption_statistics')
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            stats = data['statistics']
            print(f"   ✅ 统计API正常工作")
            print(f"      总兑换码数: {stats.get('total_codes', 0)}")
            print(f"      有效兑换码: {stats.get('active_codes', 0)}")
            print(f"      已使用: {stats.get('total_codes_used', 0)}")
            print(f"      总发放积分: {stats.get('total_points_distributed', 0)}")
        else:
            print(f"   ❌ 统计API返回错误: {data.get('message')}")
    else:
        print(f"   ❌ 统计API请求失败: {response.status_code}")
    
    # 测试兑换码列表API
    print("\n3. 测试兑换码列表API...")
    response = session.get(f'{base_url}/admin/redemption_codes')
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            codes = data['codes']
            print(f"   ✅ 兑换码列表API正常工作，共 {len(codes)} 个兑换码")
        else:
            print(f"   ❌ 兑换码列表API返回错误: {data.get('message')}")
    else:
        print(f"   ❌ 兑换码列表API请求失败: {response.status_code}")
    
    # 测试使用记录API
    print("\n4. 测试使用记录API...")
    response = session.get(f'{base_url}/admin/redemption_usage_records')
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            records = data['records']
            print(f"   ✅ 使用记录API正常工作，共 {len(records)} 条记录")
        else:
            print(f"   ❌ 使用记录API返回错误: {data.get('message')}")
    else:
        print(f"   ❌ 使用记录API请求失败: {response.status_code}")
    
    print("\n=== 测试完成 ===")
    print("✅ 后端API全部正常工作！")
    print("\n📝 如果前端仍有问题，请检查：")
    print("1. 浏览器控制台是否有JavaScript错误")
    print("2. 网络请求是否被CORS策略阻止")
    print("3. 页面是否完全加载完成")
    print("\n🌐 访问地址: http://localhost:7799")
    print("👤 管理员账户: admin / admin123")

if __name__ == '__main__':
    test_final()
