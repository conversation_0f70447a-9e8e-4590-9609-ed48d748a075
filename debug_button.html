<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试按钮问题</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <h2>调试创建兑换码按钮</h2>
        
        <!-- 测试按钮 -->
        <button id="createCodeBtn" class="btn btn-primary mb-3">
            <i class="fas fa-plus me-1"></i>创建兑换码
        </button>
        
        <div id="debugInfo" class="alert alert-info">
            <h5>调试信息：</h5>
            <div id="debugContent">等待检查...</div>
        </div>
    </div>

    <!-- 创建兑换码模态框 -->
    <div class="modal fade" id="createCodeModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus-circle me-2"></i>创建兑换码
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>模态框正常显示！</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const debugContent = document.getElementById('debugContent');
            let debugInfo = [];
            
            // 检查按钮是否存在
            const createCodeBtn = document.getElementById('createCodeBtn');
            if (createCodeBtn) {
                debugInfo.push('✅ 创建兑换码按钮找到了');
            } else {
                debugInfo.push('❌ 创建兑换码按钮未找到');
            }
            
            // 检查模态框是否存在
            const modalElement = document.getElementById('createCodeModal');
            if (modalElement) {
                debugInfo.push('✅ 模态框元素找到了');
            } else {
                debugInfo.push('❌ 模态框元素未找到');
            }
            
            // 尝试创建Bootstrap模态框实例
            let createCodeModal = null;
            try {
                createCodeModal = new bootstrap.Modal(modalElement);
                debugInfo.push('✅ Bootstrap模态框实例创建成功');
            } catch (error) {
                debugInfo.push('❌ Bootstrap模态框实例创建失败: ' + error.message);
            }
            
            // 绑定点击事件
            if (createCodeBtn && createCodeModal) {
                createCodeBtn.addEventListener('click', function() {
                    debugInfo.push('🔥 按钮被点击了！');
                    debugContent.innerHTML = debugInfo.join('<br>');
                    
                    try {
                        createCodeModal.show();
                        debugInfo.push('✅ 模态框显示成功');
                    } catch (error) {
                        debugInfo.push('❌ 模态框显示失败: ' + error.message);
                    }
                    debugContent.innerHTML = debugInfo.join('<br>');
                });
                debugInfo.push('✅ 点击事件绑定成功');
            } else {
                debugInfo.push('❌ 无法绑定点击事件');
            }
            
            // 显示调试信息
            debugContent.innerHTML = debugInfo.join('<br>');
        });
    </script>
</body>
</html>
