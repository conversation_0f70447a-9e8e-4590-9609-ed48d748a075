#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试API是否正常工作
"""

import requests

def test_api():
    base_url = 'http://localhost:7799'
    session = requests.Session()
    
    # 登录管理员
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    print("登录管理员...")
    response = session.post(f'{base_url}/login', json=login_data)
    print(f"登录响应: {response.status_code}")
    if response.status_code == 200:
        print(f"登录结果: {response.json()}")
    
    # 测试统计API
    print("\n测试统计API...")
    response = session.get(f'{base_url}/admin/redemption_statistics')
    print(f"统计API响应: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"统计数据: {data}")
    else:
        print(f"错误: {response.text}")

if __name__ == '__main__':
    test_api()
