<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端测试</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <h2>兑换码管理前端测试</h2>
        
        <!-- 统计卡片区域 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                                <i class="fas fa-ticket-alt text-primary fs-4"></i>
                            </div>
                        </div>
                        <h4 class="mb-1" id="totalCodesCount">-</h4>
                        <p class="text-muted mb-0 small">总兑换码数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <div class="bg-success bg-opacity-10 rounded-circle p-3">
                                <i class="fas fa-check-circle text-success fs-4"></i>
                            </div>
                        </div>
                        <h4 class="mb-1" id="activeCodesCount">-</h4>
                        <p class="text-muted mb-0 small">有效兑换码</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                                <i class="fas fa-clock text-warning fs-4"></i>
                            </div>
                        </div>
                        <h4 class="mb-1" id="usedCodesCount">-</h4>
                        <p class="text-muted mb-0 small">已使用</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <div class="bg-info bg-opacity-10 rounded-circle p-3">
                                <i class="fas fa-coins text-info fs-4"></i>
                            </div>
                        </div>
                        <h4 class="mb-1" id="totalPointsDistributed">-</h4>
                        <p class="text-muted mb-0 small">总发放积分</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <button id="testBtn" class="btn btn-primary">测试API调用</button>
                <div id="result" class="mt-3"></div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 显示兑换码统计
        function displayRedemptionStats(stats) {
            document.getElementById('totalCodesCount').textContent = stats.total_codes || 0;
            document.getElementById('activeCodesCount').textContent = stats.active_codes || 0;
            document.getElementById('usedCodesCount').textContent = stats.total_codes_used || 0;
            document.getElementById('totalPointsDistributed').textContent = stats.total_points_distributed || 0;
        }

        // 测试API调用
        document.getElementById('testBtn').addEventListener('click', function() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="alert alert-info">正在测试...</div>';
            
            // 先登录
            fetch('http://localhost:7799/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: 'admin',
                    password: 'admin123'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = '<div class="alert alert-success">登录成功，正在获取统计数据...</div>';
                    
                    // 获取统计数据
                    return fetch('http://localhost:7799/admin/redemption_statistics');
                } else {
                    throw new Error('登录失败: ' + data.message);
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayRedemptionStats(data.statistics);
                    resultDiv.innerHTML = '<div class="alert alert-success">统计数据加载成功！</div>';
                } else {
                    throw new Error('获取统计数据失败: ' + data.message);
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="alert alert-danger">错误: ' + error.message + '</div>';
            });
        });
    </script>
</body>
</html>
