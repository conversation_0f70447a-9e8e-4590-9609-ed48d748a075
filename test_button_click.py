#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试创建兑换码按钮点击
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import time

def test_button_click():
    print("=== 测试创建兑换码按钮点击 ===\n")
    
    # 设置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    try:
        # 启动浏览器
        driver = webdriver.Chrome(options=chrome_options)
        driver.get("http://localhost:7799")
        
        print("1. 页面加载完成")
        
        # 等待页面加载
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        # 登录（如果需要）
        try:
            login_btn = driver.find_element(By.ID, "loginBtn")
            if login_btn.is_displayed():
                print("2. 需要登录，正在登录...")
                username_input = driver.find_element(By.ID, "username")
                password_input = driver.find_element(By.ID, "password")
                
                username_input.send_keys("admin")
                password_input.send_keys("admin123")
                login_btn.click()
                
                time.sleep(2)
        except:
            print("2. 已经登录或不需要登录")
        
        # 点击管理面板
        try:
            admin_btn = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.ID, "adminBtn"))
            )
            admin_btn.click()
            print("3. 点击管理面板成功")
            time.sleep(1)
        except Exception as e:
            print(f"3. 点击管理面板失败: {e}")
            return
        
        # 切换到兑换码管理标签页
        try:
            redemption_tab = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, 'a[href="#redemptionTab"]'))
            )
            redemption_tab.click()
            print("4. 切换到兑换码管理标签页成功")
            time.sleep(2)
        except Exception as e:
            print(f"4. 切换到兑换码管理标签页失败: {e}")
            return
        
        # 查找创建兑换码按钮
        try:
            create_btn = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.ID, "createCodeBtn"))
            )
            print("5. 找到创建兑换码按钮")
            
            # 点击按钮
            create_btn.click()
            print("6. 点击创建兑换码按钮成功")
            
            # 等待模态框出现
            modal = WebDriverWait(driver, 5).until(
                EC.visibility_of_element_located((By.ID, "createCodeModal"))
            )
            print("7. ✅ 创建兑换码模态框显示成功！")
            
        except Exception as e:
            print(f"5-7. 创建兑换码按钮操作失败: {e}")
            
            # 检查控制台错误
            logs = driver.get_log('browser')
            if logs:
                print("\n浏览器控制台错误:")
                for log in logs:
                    print(f"  {log['level']}: {log['message']}")
        
        time.sleep(3)
        
    except Exception as e:
        print(f"测试失败: {e}")
    finally:
        try:
            driver.quit()
        except:
            pass

if __name__ == '__main__':
    test_button_click()
